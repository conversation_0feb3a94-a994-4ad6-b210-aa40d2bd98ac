/** @format */

// Wizlop Color System - Centralized color palette
// Based on Logo 5 gradient colors for consistent branding

export const colors = {
	// 1. Primary Brand Colors (3 core brand colors)
	brand: {
		blue: '#33C2FF', // Bright Cyan Blue - Main brand anchor (buttons, nav, footer)
		navy: '#01034F', // Deep Navy - Secondary brand tone (hover, text, headers)
		green: '#80ED99', // Light Green - Nature-focused accent (tabs, highlights)
	},

	// 2. Supporting Colors (6 supporting colors)
	supporting: {
		lightBlue: '#66D0FF', // Light Blue - Background accents, cards, hover states
		softNavy: '#1A1F5C', // Soft Navy - Subtle backgrounds, borders
		mintGreen: '#A3F7B5', // Mint Green - Success states, positive accents
		teal: '#26A69A', // Teal - Additional UI elements (moved from brand)
		darkBlue: '#1565C0', // Dark blue - Additional UI elements (moved from brand)
		purple: '#8B5CF6', // Purple - Additional accent color
	},

	// 3. Black & White / Neutrals
	neutral: {
		textBlack: '#1B1B1E', // Text Black - Primary text, logo variant
		slateGray: '#5E6E7E', // Slate Gray - Secondary text, inputs
		cloudWhite: '#F5F7F8', // Cloud White - Background
		lightMistGray: '#E6F2F0', // Light Mist Gray - Card background, hover fill
	},

	// 4. Utility Colors
	utility: {
		success: '#4CAF50',
		warning: '#FFB74D',
		error: '#E57373',
		errorLight: '#FFCDD2',
		info: '#29B6F6',
	},

	// 5. UI Colors (only commonly used ones)
	ui: {
		// Light variations for backgrounds
		blue50: '#F0FAFF', // Very light cyan blue
		blue100: '#E1F5FF', // Light cyan blue
		blue200: '#B3E5FF', // Medium light cyan blue

		navy50: '#F8F8FB', // Very light navy

		green50: '#F5FDF8', // Very light green
		green100: '#EBFBF0', // Light green
		green200: '#D7F7E1', // Medium light green

		// Essential gray scale
		gray50: '#F9FAFB',
		gray100: '#F3F4F6',
		gray200: '#E5E7EB',
		gray400: '#9CA3AF',
		gray500: '#6B7280',
	},
} as const;

// Tailwind CSS custom color classes
export const tailwindColors = {
	// Primary brand colors
	'wizlop-blue': colors.brand.blue,
	'wizlop-navy': colors.brand.navy,
	'wizlop-green': colors.brand.green,

	// Supporting colors
	'wizlop-light-blue': colors.supporting.lightBlue,
	'wizlop-soft-navy': colors.supporting.softNavy,
	'wizlop-mint-green': colors.supporting.mintGreen,
	'wizlop-teal': colors.supporting.teal,
	'wizlop-dark-blue': colors.supporting.darkBlue,
	'wizlop-purple': colors.supporting.purple,

	// Neutrals
	'wizlop-text': colors.neutral.textBlack,
	'wizlop-slate': colors.neutral.slateGray,
	'wizlop-cloud': colors.neutral.cloudWhite,
	'wizlop-mist': colors.neutral.lightMistGray,
} as const;

// Gradient combinations
export const gradients = {
	primary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
	secondary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
	soft: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.supporting.mintGreen} 100%)`,
	background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
} as const;

// Common color utilities
export const getColorWithOpacity = (color: string, opacity: number): string => {
	return `${color}${Math.round(opacity * 255)
		.toString(16)
		.padStart(2, '0')}`;
};

export default colors;
