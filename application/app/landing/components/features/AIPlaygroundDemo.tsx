/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import {
	FiArrowRight,
	FiCheckCircle,
	FiMessageCircle,
	FiSearch,
	FiStar,
} from 'react-icons/fi';

interface DemoScenario {
	id: string;
	userQuery: string;
	aiResponse: string;
	searchResults: LocationResult[];
	processingSteps: string[];
	conversationContext: string[];
	traditionalSteps: string[];
}

interface LocationResult {
	name: string;
	type: string;
	rating: number;
	distance: string;
	features: string[];
	image?: string;
}

const demoScenarios: DemoScenario[] = LANDING_PAGE_DATA.aiDemoScenarios.map(
	(scenario) => ({
		...scenario,
		conversationContext: [],
	})
);

const ProcessingStep: React.FC<{
	step: string;
	index: number;
	isActive: boolean;
	isCompleted: boolean;
}> = ({ step, index, isActive, isCompleted }) => (
	<div
		className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${
			isActive ? 'scale-105' : ''
		}`}
		style={{
			background: isActive
				? `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`
				: isCompleted
				? colors.ui.green50
				: colors.ui.gray50,
			borderLeft: `4px solid ${
				isCompleted
					? colors.brand.green
					: isActive
					? colors.brand.blue
					: colors.ui.gray200
			}`,
		}}>
		<div
			className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 ${
				isActive ? 'animate-pulse' : ''
			}`}
			style={{
				background: isCompleted
					? colors.brand.green
					: isActive
					? colors.brand.blue
					: colors.ui.gray200,
			}}>
			{isCompleted ? (
				<FiCheckCircle className='w-4 h-4 text-white' />
			) : (
				<span className='text-xs font-bold text-white'>{index + 1}</span>
			)}
		</div>
		<span
			className={`text-sm ${isActive ? 'font-semibold' : ''}`}
			style={{
				color: isActive ? colors.neutral.textBlack : colors.neutral.slateGray,
			}}>
			{step}
		</span>
	</div>
);

const LocationCard: React.FC<{ location: LocationResult; index: number }> = ({
	location,
	index,
}) => (
	<div
		className='p-4 rounded-xl border transition-all duration-300 hover:scale-102'
		style={{
			background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
			borderColor: colors.ui.gray200,
			animationDelay: `${index * 100}ms`,
		}}>
		<div className='flex items-start justify-between mb-3'>
			<div>
				<h4
					className='font-semibold'
					style={{ color: colors.neutral.textBlack }}>
					{location.name}
				</h4>
				<p
					className='text-sm'
					style={{ color: colors.neutral.slateGray }}>
					{location.type} • {location.distance}
				</p>
			</div>
			<div className='flex items-center space-x-1'>
				<FiStar
					className='w-4 h-4'
					style={{ color: colors.brand.green }}
				/>
				<span
					className='text-sm font-medium'
					style={{ color: colors.neutral.textBlack }}>
					{location.rating}
				</span>
			</div>
		</div>

		<div className='flex flex-wrap gap-2'>
			{location.features.slice(0, 3).map((feature, idx) => (
				<span
					key={idx}
					className='px-2 py-1 rounded-full text-xs'
					style={{
						background: colors.ui.blue50,
						color: colors.neutral.slateGray,
					}}>
					{feature}
				</span>
			))}
		</div>
	</div>
);

const TraditionalSearchSteps: React.FC<{ steps: string[] }> = ({ steps }) => (
	<div className='space-y-3'>
		{steps.map((step, index) => (
			<div
				key={index}
				className='flex items-center space-x-3 p-3 rounded-lg'
				style={{ background: colors.ui.gray50 }}>
				<div
					className='w-6 h-6 rounded-full flex items-center justify-center'
					style={{ background: colors.ui.gray200 }}>
					<span className='text-xs font-bold text-gray-600'>{index + 1}</span>
				</div>
				<span
					className='text-sm'
					style={{ color: colors.neutral.slateGray }}>
					{step}
				</span>
			</div>
		))}
	</div>
);

const AIPlaygroundDemo: React.FC<{ onGetStarted?: () => void }> = ({
	onGetStarted,
}) => {
	const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
	const [currentStep, setCurrentStep] = useState(0);
	const [userText, setUserText] = useState('');
	const [aiText, setAIText] = useState('');
	const [isTyping, setIsTyping] = useState(false);
	const [showUserMessage, setShowUserMessage] = useState(false);
	const [showAIResponse, setShowAIResponse] = useState(false);
	const [showResults, setShowResults] = useState(false);

	const currentScenario = demoScenarios[currentScenarioIndex];

	// Looping animation cycle similar to HeroPreview
	useEffect(() => {
		const animationCycle = async () => {
			// Reset all states
			setShowUserMessage(false);
			setShowAIResponse(false);
			setShowResults(false);
			setUserText('');
			setAIText('');
			setIsTyping(false);
			setCurrentStep(0);

			// Wait before starting
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Type user message
			setShowUserMessage(true);
			for (let i = 0; i <= currentScenario.userQuery.length; i++) {
				setUserText(currentScenario.userQuery.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 60));
			}

			// Wait before AI processing
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Show typing indicator and processing steps
			setIsTyping(true);
			for (
				let step = 0;
				step < currentScenario.processingSteps.length;
				step++
			) {
				setCurrentStep(step);
				await new Promise((resolve) => setTimeout(resolve, 1200));
			}

			// Type AI response
			setIsTyping(false);
			setShowAIResponse(true);
			for (let i = 0; i <= currentScenario.aiResponse.length; i++) {
				setAIText(currentScenario.aiResponse.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 40));
			}

			// Show results
			await new Promise((resolve) => setTimeout(resolve, 800));
			setShowResults(true);

			// Wait before next cycle
			await new Promise((resolve) => setTimeout(resolve, 5000));

			// Move to next scenario
			setCurrentScenarioIndex((prev) => (prev + 1) % demoScenarios.length);
		};

		animationCycle();
	}, [
		currentScenarioIndex,
		currentScenario.userQuery,
		currentScenario.aiResponse,
		currentScenario.processingSteps.length,
	]);

	return (
		<div className='w-full'>
			{/* Clean Demo Container */}
			<div className='max-w-4xl mx-auto'>
				{/* Simple Progress Dots */}
				<div className='flex justify-center mb-12'>
					<div className='flex space-x-3'>
						{demoScenarios.map((_, index) => (
							<div
								key={index}
								className='w-3 h-3 rounded-full transition-all duration-500'
								style={{
									background: currentScenarioIndex === index ? colors.brand.blue : colors.ui.gray300,
									transform: currentScenarioIndex === index ? 'scale(1.3)' : 'scale(1)',
								}}
								/>
							))}
						</div>
					</div>

				{/* Simplified Single Demo Area */}
				<div className='mb-12'>
					{/* Clean Demo Container */}
					<div
						className='p-8 rounded-2xl border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.blue50}30 0%, ${colors.neutral.cloudWhite} 100%)`,
							borderColor: colors.ui.gray200,
							boxShadow: '0 4px 20px rgba(51, 194, 255, 0.06)',
						}}>
						{/* Scenario Title */}
						<div className='text-center mb-8'>
							<h3
								className='text-xl font-semibold'
								style={{ color: colors.neutral.textBlack }}>
								{currentScenario.id === 'cozy-cafe' ? '☕ Finding a Work Cafe' : '🍽️ Planning a Romantic Dinner'}
							</h3>
						</div>

							{/* User Query */}
						{showUserMessage && (
							<div className='mb-6'>
								<div
									className='p-4 rounded-xl'
									style={{
										background: colors.brand.blue,
										color: 'white',
										marginLeft: 'auto',
										marginRight: '0',
										maxWidth: '80%',
									}}>
									<p className='text-sm'>
										"{userText}<span className='animate-pulse'>|</span>"
									</p>
								</div>
							</div>
						)}

						{/* Simple Processing Indicator */}
							<div className='text-center mb-6'>
								<div className='flex items-center justify-center space-x-2'>
									<div className='flex space-x-1'>
										<div className='w-2 h-2 rounded-full bg-blue-500 animate-pulse'></div>
										<div className='w-2 h-2 rounded-full bg-blue-500 animate-pulse' style={{ animationDelay: '0.2s' }}></div>
										<div className='w-2 h-2 rounded-full bg-blue-500 animate-pulse' style={{ animationDelay: '0.4s' }}></div>
									</div>
									<span className='text-sm text-gray-600 ml-3'>AI is thinking...</span>
								</div>
							</div>
						)}
					</div>
				</div>

				{/* AI Response */}
				{showAIResponse && (
					<div className='mb-8'>
						<div
							className='p-4 rounded-xl'
							style={{
								background: colors.ui.gray100,
								maxWidth: '80%',
							}}>
							<p className='text-sm' style={{ color: colors.neutral.textBlack }}>
								{aiText}<span className='animate-pulse'>|</span>
							</p>
						</div>
					</div>
				)}

				{/* Simple Results */}
				{showResults && (
					<div className='text-center'>
						<div
							className='inline-flex items-center space-x-2 px-6 py-3 rounded-xl'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.green}20 0%, ${colors.brand.blue}20 100%)`,
								border: `1px solid ${colors.brand.green}40`,
							}}>
							<span className='text-green-600'>✓</span>
							<span className='text-sm font-medium' style={{ color: colors.neutral.textBlack }}>
								Found {currentScenario.searchResults.length} perfect matches
							</span>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default AIPlaygroundDemo;
